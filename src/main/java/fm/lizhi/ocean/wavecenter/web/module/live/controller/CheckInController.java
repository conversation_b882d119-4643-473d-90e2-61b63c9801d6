package fm.lizhi.ocean.wavecenter.web.module.live.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.platform.api.platform.request.*;
import fm.lizhi.ocean.wave.platform.api.platform.response.ResponseGetCheckInConfig;
import fm.lizhi.ocean.wave.platform.api.platform.service.WaveCheckInManagementService;
import fm.lizhi.ocean.wavecenter.api.common.service.CommonService;
import fm.lizhi.ocean.wavecenter.api.live.bean.*;
import fm.lizhi.ocean.wavecenter.api.live.constants.CheckInDateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.live.request.RequestGetCheckInPlayerStatistic;
import fm.lizhi.ocean.wavecenter.api.live.request.RequestGetCheckInPlayerSum;
import fm.lizhi.ocean.wavecenter.api.live.request.RequestGetCheckInRoomStatistic;
import fm.lizhi.ocean.wavecenter.api.live.request.RequestGetCheckInRoomSum;
import fm.lizhi.ocean.wavecenter.api.live.response.*;
import fm.lizhi.ocean.wavecenter.api.live.service.LiveService;
import fm.lizhi.ocean.wavecenter.api.live.service.WaveCheckInDataService;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.sign.service.SignAdminService;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.RoomSignBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.api.user.service.UserCommonService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.PageVO;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.module.file.handler.DynamicColTable;
import fm.lizhi.ocean.wavecenter.web.module.file.handler.FileExportHandler;
import fm.lizhi.ocean.wavecenter.web.module.live.constant.CheckInMetricsEnum;
import fm.lizhi.ocean.wavecenter.web.module.live.model.converter.CheckInConverter;
import fm.lizhi.ocean.wavecenter.web.module.live.model.param.*;
import fm.lizhi.ocean.wavecenter.web.module.live.model.result.*;
import fm.lizhi.ocean.wavecenter.web.module.live.vo.check.CheckInHostConfigVo;
import fm.lizhi.ocean.wavecenter.web.module.permission.handler.DataScopeHandler;
import fm.lizhi.ocean.wavecenter.web.module.user.convert.UserCommonConvert;
import fm.lizhi.ocean.wavecenter.web.module.user.handler.UserFamilyHandler;
import fm.lizhi.ocean.wavecenter.web.module.user.handler.UserHandler;
import fm.lizhi.ocean.wavecenter.web.module.user.vo.UserVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 麦序福利控制器.
 */
@RestController
@RequestMapping("/live/checkIn")
@Slf4j
public class CheckInController {

    @Autowired
    private CheckInConverter checkInConverter;

    @Autowired
    private DataScopeHandler dataScopeHandler;

    @Autowired
    private FileExportHandler fileExportHandler;

    @Autowired
    private UserFamilyHandler userFamilyHandler;

    @Autowired
    private UserHandler userHandler;

    @Autowired
    private LiveService liveService;

    @Autowired
    private SignAdminService signAdminService;

    @Autowired
    private WaveCheckInDataService waveCheckInDataService;

    @Autowired
    private WaveCheckInManagementService waveCheckInManagementService;

    @Autowired
    private UserCommonService userCommonService;

    /**
     * 保存麦序福利表主持人配置
     */
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.ROOM})
    @PostMapping("/config/saveHost")
    public ResultVO<Void> saveHost(@RequestBody SaveCheckInHostConfigParam param) {

        if (StringUtils.isBlank(param.getStartDate()) || StringUtils.isBlank(param.getEndDate())) {
            return ResultVO.failure("请选择日期");
        }
        // 小于当前周不可编辑
        if (DateUtil.endOfDay(DateUtil.parse(param.getEndDate())).getTime() < new Date().getTime()) {
            return ResultVO.failure("历史周不可保存");
        }

        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Long familyId = dataScopeHandler.getFamilyForFamilyOrRoom();
        Long njId = ContextUtils.getContext().getSubjectId();
        ContextUtils.getContext().addReqLog("`familyId={}", familyId);
        ContextUtils.getContext().addReqLog("`njId={}", njId);

        Result<Long> roomIdByNjId = getRoomIdByNjId(appId, njId);
        if (RpcResult.isFail(roomIdByNjId)) {
            return ResultVO.failure(roomIdByNjId.getMessage());
        }
        Long roomId = roomIdByNjId.target();
        ContextUtils.getContext().addReqLog("`roomId={}", roomId);

        RequestSaveCheckInHostConfig req = checkInConverter.toRequestSaveCheckInHostConfig(param, appId, familyId, roomId);
        Result<Void> result = waveCheckInManagementService.saveCheckInHostConfig(req);
        if (RpcResult.isFail(result)) {
            log.warn("saveCheckInHostConfig fail, req: {}, rCode: {}, message: {}", param, result.rCode(), result.getMessage());
            return ResultVO.failure(result.getMessage());
        }
        return ResultVO.success();
    }

    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.ROOM, RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN})
    @GetMapping("/config/getHost")
    public ResultVO<GetCheckInHostConfigResult> getHost(
            @RequestParam(value = "startDate") String startDate,
            @RequestParam(value = "endDate") String endDate,
            @RequestParam(value = "njId", required = false) Long reqNjId
    ) {
        // 如果不是厅主请求，需要传njId
        if (!ContextUtils.getContext().isRoom() && reqNjId == null) {
            return ResultVO.failure("请选择厅");
        }
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Long familyId = dataScopeHandler.getFamilyForFamilyOrRoom();
        Long njId = ContextUtils.getContext().isRoom()? ContextUtils.getContext().getSubjectId() : reqNjId;
        ContextUtils.getContext().addReqLog("`familyId={}", familyId);
        ContextUtils.getContext().addReqLog("`njId={}", njId);

        Result<Long> roomIdByNjId = getRoomIdByNjId(appId, njId);
        if (RpcResult.isFail(roomIdByNjId)) {
            return ResultVO.failure(roomIdByNjId.getMessage());
        }

        Long roomId = roomIdByNjId.target();
        ContextUtils.getContext().addReqLog("`roomId={}", roomId);

        RequestGetCheckInHostConfig req = new RequestGetCheckInHostConfig();
        req.setAppId(appId);
        req.setFamilyId(familyId);
        req.setRoomId(roomId);
        req.setStartTime(DateUtil.parseDate(startDate));
        req.setEndTime(DateUtil.parseDate(endDate));
        Result<ResponseGetCheckInHostConfig> result = waveCheckInManagementService.getCheckInHostConfig(req);
        if (RpcResult.isFail(result)) {
            log.warn("getCheckInHostConfig fail, req: {}, rCode: {}, message: {}", req, result.rCode(), result.getMessage());
            return ResultVO.failure(result.getMessage());
        }
        ResponseGetCheckInHostConfig resp = result.target();
        GetCheckInHostConfigResult getResult = checkInConverter.toGetCheckInHostConfigResult(resp);

        // 填充用户信息
        if (getResult != null && CollUtil.isNotEmpty(getResult.getHostDetails())) {
            List<Long> hostIds = getResult.getHostDetails().stream().map(CheckInHostConfigVo::getHostId).collect(Collectors.toList());

            Result<List<UserBean>> hostInfoList = userCommonService.getUserByIds(appId, hostIds);
            if (RpcResult.isSuccess(hostInfoList)) {
                Map<Long, UserBean> hostInfoMap = hostInfoList.target().stream().collect(Collectors.toMap(UserBean::getId, Function.identity(), (a, b) -> a));
                getResult.getHostDetails().forEach(host -> {
                    UserBean userBean = hostInfoMap.get(host.getHostId());
                    host.setHostInfo(UserCommonConvert.I.userBean2Vo(userBean));
                });
            }
        }
        return ResultVO.success(getResult);
    }

    /**
     * 保存麦序福利配置
     *
     * @param param 参数
     * @return 结果
     */
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.ROOM})
    @PostMapping("/config/save")
    public ResultVO<Void> saveCheckInConfig(@RequestBody SaveCheckInConfigParam param) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Long familyId = dataScopeHandler.getFamilyForFamilyOrRoom();
        ContextUtils.getContext().addReqLog("`familyId={}", familyId);
        Long njId = ContextUtils.getContext().getSubjectId();
        ContextUtils.getContext().addReqLog("`njId={}", njId);
        Result<Long> roomIdByNjId = getRoomIdByNjId(appId, njId);
        if (RpcResult.isFail(roomIdByNjId)) {
            return ResultVO.failure(roomIdByNjId.getMessage());
        }
        Long roomId = roomIdByNjId.target();
        ContextUtils.getContext().addReqLog("`roomId={}", roomId);

        if (param.getDayMicConfig() != null && (param.getDayMicConfig().getMaxCount() == null || param.getDayMicConfig().getMaxCount() <= 0)) {
            param.getDayMicConfig().setMaxCount(Integer.MAX_VALUE);
        }

        RequestSaveCheckInConfig req = checkInConverter.toRequestSaveCheckInConfig(param, appId, familyId, roomId, njId);
        Result<Void> result = waveCheckInManagementService.saveCheckInConfig(req);
        if (RpcResult.isFail(result)) {
            log.info("saveCheckInConfig fail, req: {}, rCode: {}, message: {}", req, result.rCode(), result.getMessage());
            if (result.rCode() == CommonService.PARAM_ERROR) {
                return ResultVO.failure(result.getMessage());
            } else {
                return ResultVO.failure();
            }
        }
        log.info("saveCheckInConfig success, req: {}", req);
        return ResultVO.success();
    }

    private Result<Long> getRoomIdByNjId(int appId, Long njId) {
        Result<ResponseGetRoomInfoByNjId> result = liveService.getRoomInfoByNjId(appId, njId);
        if (RpcResult.isFail(result)) {
            if (result.rCode() == LiveService.GET_ROOM_INFO_BY_NJ_ID_NO_EXIST) {
                log.info("getRoomInfoByNjId no exist, appId: {}, njId: {}", appId, njId);
                return RpcResult.fail(LiveService.GET_ROOM_INFO_BY_NJ_ID_NO_EXIST, "找不到厅信息, 该主播可能没有开播过");
            } else {
                log.info("getRoomInfoByNjId fail, appId: {}, njId: {}, rCode: {}, message: {}", appId, njId, result.rCode(), result.getMessage());
                return RpcResult.fail(GeneralRCode.GENERAL_RCODE_UNKNOWN_ERROR, "获取厅信息失败");
            }
        }
        return RpcResult.success(result.target().getId());
    }

    /**
     * 获取麦序福利配置
     *
     * @param reqNjId 厅主id
     * @return 结果
     */
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.ROOM, RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN})
    @GetMapping("/config/get")
    public ResultVO<GetCheckInConfigResult> getCheckInConfig(@RequestParam(value = "njId", required = false) Long reqNjId) {
        // 如果不是厅主请求，需要传njId
        if (!ContextUtils.getContext().isRoom() && reqNjId == null) {
            return ResultVO.failure("请选择厅");
        }
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Long familyId = dataScopeHandler.getFamilyForFamilyOrRoom();
        ContextUtils.getContext().addReqLog("`familyId={}", familyId);
        Long njId = ContextUtils.getContext().isRoom() ? ContextUtils.getContext().getSubjectId() : reqNjId;
        ContextUtils.getContext().addReqLog("`njId={}", njId);
        Result<Long> roomIdByNjId = getRoomIdByNjId(appId, njId);
        if (RpcResult.isFail(roomIdByNjId)) {
            return ResultVO.failure(roomIdByNjId.getMessage());
        }
        Long roomId = roomIdByNjId.target();
        ContextUtils.getContext().addReqLog("`roomId={}", roomId);
        RequestGetCheckInConfig req = checkInConverter.toRequestGetCheckInConfig(appId, familyId, roomId);
        Result<ResponseGetCheckInConfig> result = waveCheckInManagementService.getCheckInConfig(req);
        if (RpcResult.isFail(result)) {
            log.info("getCheckInConfig fail, req: {}, rCode: {}, message: {}", req, result.rCode(), result.getMessage());
            if (result.rCode() == CommonService.PARAM_ERROR) {
                return ResultVO.failure(result.getMessage());
            } else {
                return ResultVO.failure();
            }
        }
        ResponseGetCheckInConfig resp = result.target();
        log.info("getCheckInConfig success, resp: {}", resp);

        List<UserVo> managerList = new ArrayList<>();
        if (CollUtil.isNotEmpty(resp.getCheckInManagerConfig())){
            List<UserBean> managerBeanList = userHandler.getUserByIds(resp.getCheckInManagerConfig());
            managerList = checkInConverter.userBeans2Vos(managerBeanList);
        }

        return ResultVO.success(checkInConverter.toGetCheckInConfigResult(resp, managerList));
    }

    /**
     * 获取麦序福利厅汇总.
     * <ul>
     *     <li>厅主视角: 不限家族, 当前厅, 的数据</li>
     *     <li>家族长视角: 当前家族, 选中厅, 的数据</li>
     * </ul>
     *
     * @param param 参数
     * @return 结果
     */
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.ROOM, RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN})
    @GetMapping("/room/sum")
    public ResultVO<GetCheckInRoomSumResult> getCheckInRoomSum(GetCheckInRoomSumParam param) {
        // 如果不是厅主请求，需要传njId
        if (!ContextUtils.getContext().isRoom() && param.getNjId() == null) {
            return ResultVO.failure("请选择厅");
        }
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Long njId = ContextUtils.getContext().isRoom() ? ContextUtils.getContext().getSubjectId() : param.getNjId();
        ContextUtils.getContext().addReqLog("`njId={}`startDate={}`endDate={}", njId, param.getStartDate(), param.getEndDate());
        Result<Long> roomIdByNjId = getRoomIdByNjId(appId, njId);
        if (RpcResult.isFail(roomIdByNjId)) {
            return ResultVO.failure(roomIdByNjId.getMessage());
        }
        Long roomId = roomIdByNjId.target();
        ContextUtils.getContext().addReqLog("`roomId={}", roomId);
        // 注意如果加高级管理角色, subjectId 也是familyId
        Long familyId = dataScopeHandler.getFamilyForFamily();
        ContextUtils.getContext().addReqLog("`familyId={}", familyId);
        // 	签约主播数
        Integer signPlayerCnt = getSignPlayerCnt(appId, njId);
        if (signPlayerCnt == null) {
            return ResultVO.failure("获取厅签约信息失败");
        }
        // 其他数据
        RequestGetCheckInRoomSum req = checkInConverter.toRequestGetCheckInRoomSum(param, appId, roomId, familyId);
        Result<ResponseGetCheckInRoomSum> result = waveCheckInDataService.getCheckInRoomSum(req);
        if (RpcResult.isFail(result)) {
            log.info("getCheckInRoomSum fail, req: {}, rCode: {}, message: {}", req, result.rCode(), result.getMessage());
            if (result.rCode() == CommonService.PARAM_ERROR) {
                return ResultVO.failure(result.getMessage());
            } else {
                return ResultVO.failure();
            }
        }
        ResponseGetCheckInRoomSum resp = result.target();
        // 合并结果
        GetCheckInRoomSumResult getResult = checkInConverter.toGetCheckInRoomSumResult(resp, signPlayerCnt);
        log.info("getCheckInRoomSum success, result: {}", getResult);
        return ResultVO.success(getResult);
    }

    private Integer getSignPlayerCnt(int appId, Long njId) {
        Result<Integer> result = signAdminService.countSignPlayerNum(appId, njId);
        if (RpcResult.isFail(result)) {
            log.info("countSignPlayerNum fail, appId: {}, njId: {}, rCode: {}, message: {}", appId, njId, result.rCode(), result.getMessage());
            return null;
        }
        return result.target();
    }

    /**
     * 获取麦序福利厅详情
     * <ul>
     *     <li>厅主视角: 不限家族, 当前厅, 的数据</li>
     *     <li>家族长视角: 当前家族, 选中厅, 的数据</li>
     * </ul>
     *
     * @param param 参数
     * @return 结果
     */
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.ROOM, RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN})
    @GetMapping("/room/detail")
    public ResultVO<GetCheckInRoomDetailResult> getCheckInRoomDetail(GetCheckInRoomDetailParam param) {
        // 如果不是厅主请求，需要传njId
        if (!ContextUtils.getContext().isRoom() && param.getNjId() == null) {
            return ResultVO.failure("请选择厅");
        }
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Long njId = ContextUtils.getContext().isRoom() ? ContextUtils.getContext().getSubjectId() : param.getNjId();
        ContextUtils.getContext().addReqLog("`njId={}`dateType={}`startDate={}`endDate={}",
                njId, param.getDateType(), param.getStartDate(), param.getEndDate());
        Result<Long> roomIdByNjId = getRoomIdByNjId(appId, njId);
        if (RpcResult.isFail(roomIdByNjId)) {
            return ResultVO.failure(roomIdByNjId.getMessage());
        }
        Long roomId = roomIdByNjId.target();
        ContextUtils.getContext().addReqLog("`roomId={}", roomId);
        // 注意如果加高级管理角色, subjectId 也是familyId
        Long familyId = dataScopeHandler.getFamilyForFamily();
        ContextUtils.getContext().addReqLog("`familyId={}", familyId);
        RequestGetCheckInRoomStatistic req = checkInConverter.toRequestGetCheckInRoomStatistic(param, appId, roomId, familyId);
        Result<ResponseGetCheckInRoomStatistic> result = waveCheckInDataService.getCheckInRoomStatistic(req);
        if (RpcResult.isFail(result)) {
            log.info("getCheckInRoomDetail fail, req: {}, rCode: {}, message: {}", req, result.rCode(), result.getMessage());
            if (result.rCode() == CommonService.PARAM_ERROR) {
                return ResultVO.failure(result.getMessage());
            } else {
                return ResultVO.failure();
            }
        }
        ResponseGetCheckInRoomStatistic resp = result.target();
        if (log.isDebugEnabled()) {
            log.debug("getCheckInRoomDetail success, resp: {}", JsonUtil.dumps(resp));
        }
        GetCheckInRoomDetailResult vo = checkInConverter.toGetCheckInRoomDetailResult(resp);
        // 计算行合计
        WaveCheckInRoomStatisticBean sum = sumWaveCheckInRoomStatisticList(resp.getList());
        vo.setDetailSum(checkInConverter.waveCheckInRoomStatisticBean2Statistic(sum));
        return ResultVO.success(vo);
    }

    /**
     * 获取麦序福利厅详情 行合计
     * @param list
     * @return
     */
    private WaveCheckInRoomStatisticBean sumWaveCheckInRoomStatisticList(List<WaveCheckInRoomStatisticBean> list) {
        WaveCheckInRoomStatisticBean total = new WaveCheckInRoomStatisticBean();
        if (CollectionUtils.isEmpty(list)) {
            return total;
        }

        // 1. 初始化汇总对象
        WaveCheckInUserSumBean sumBean = new WaveCheckInUserSumBean();


        total.setSum(sumBean);

        // 2. 创建按time分组的detail合并容器
        Map<String, WaveCheckInUserRecordSumBean> mergedDetails = new LinkedHashMap<>();

        // 3. 遍历所有记录
        for (WaveCheckInRoomStatisticBean bean : list) {
            if (bean == null || bean.getSum() == null) {
                continue;
            }

            // 4. 累加sum字段数值
            WaveCheckInUserSumBean currentSum = bean.getSum();
            sumBean.setScheduledCnt(sumBean.getScheduledCnt() + currentSum.getScheduledCnt());
            sumBean.setSeatCnt(sumBean.getSeatCnt() + currentSum.getSeatCnt());
            sumBean.setSeatCharm(sumBean.getSeatCharm() + currentSum.getSeatCharm());
            sumBean.setHostCnt(sumBean.getHostCnt() + currentSum.getHostCnt());
            sumBean.setHostCharmSum(sumBean.getHostCharmSum() + currentSum.getHostCharmSum());
            sumBean.setSumCharm(sumBean.getSumCharm() + currentSum.getSumCharm());
            sumBean.setSumIncome(sumBean.getSumIncome() + currentSum.getSumIncome());
            sumBean.setNotDoneScore(sumBean.getNotDoneScore() + currentSum.getNotDoneScore());
            sumBean.setLightGiftAmount(sumBean.getLightGiftAmount() + currentSum.getLightGiftAmount());
            sumBean.setAllMicGiftAmount(sumBean.getAllMicGiftAmount() + currentSum.getAllMicGiftAmount());
            sumBean.setDayMicAmount(sumBean.getDayMicAmount() + currentSum.getDayMicAmount());

            // 5. 合并detail字段
            if (CollectionUtils.isNotEmpty(bean.getDetail())) {
                for (WaveCheckInUserRecordSumBean detail : bean.getDetail()) {
                    if (detail == null || StringUtils.isBlank(detail.getTime())) {
                        continue;
                    }
                    WaveCheckInUserRecordSumBean detailSum = mergedDetails.computeIfAbsent(detail.getTime(), k -> new WaveCheckInUserRecordSumBean());
                    detailSum.setTime(detail.getTime());
                    detailSum.setCharm(detailSum.getCharm() + detail.getCharm());
                    detailSum.setIncome(detailSum.getIncome() + detail.getIncome());
                }
            }
        }

        // 6. 设置合并后的detail
        total.setDetail(new ArrayList<>(mergedDetails.values()));
        return total;
    }

    public static void main(String[] args) {
        String dataJson = "[{\"detail\":[{\"charm\":2,\"income\":2,\"time\":\"02/24\"},{\"charm\":0,\"income\":0,\"time\":\"02/25\"},{\"charm\":23,\"income\":8,\"time\":\"02/26\"},{\"charm\":0,\"income\":0,\"time\":\"02/27\"},{\"charm\":50368,\"income\":162,\"time\":\"02/28\"},{\"charm\":0,\"income\":0,\"time\":\"03/01\"},{\"charm\":0,\"income\":0,\"time\":\"03/02\"}],\"player\":{\"band\":\"180005908\",\"id\":1369753360190123266,\"name\":\"的话堵得慌北戴河读读\",\"photo\":\"https://cdnoffice.lizhi.fm/user/2023/06/13/3013806376780845570.jpg\"},\"sum\":{\"allMicGift\":\"\",\"allMicGiftAmount\":0,\"dayMicAmount\":0,\"hostCharmSum\":0,\"hostCnt\":0,\"lightGift\":\"100*162\",\"lightGiftAmount\":162,\"notDoneScore\":0,\"scheduledCnt\":0,\"seatCharm\":0,\"seatCnt\":0,\"sumCharm\":50393,\"sumIncome\":172}},{\"detail\":[{\"charm\":2,\"income\":2,\"time\":\"02/24\"},{\"charm\":0,\"income\":0,\"time\":\"02/25\"},{\"charm\":22,\"income\":7,\"time\":\"02/26\"},{\"charm\":0,\"income\":0,\"time\":\"02/27\"},{\"charm\":48928,\"income\":157,\"time\":\"02/28\"},{\"charm\":0,\"income\":0,\"time\":\"03/01\"},{\"charm\":0,\"income\":0,\"time\":\"03/02\"}],\"player\":{\"band\":\"180005216\",\"id\":1369754036646975490,\"name\":\"长昵称测试长昵称测试\",\"photo\":\"https://cdnoffice.lizhi.fm/user/2023/06/13/3013806963043913730.jpg\"},\"sum\":{\"allMicGift\":\"\",\"allMicGiftAmount\":0,\"dayMicAmount\":0,\"hostCharmSum\":0,\"hostCnt\":0,\"lightGift\":\"100*157\",\"lightGiftAmount\":157,\"notDoneScore\":0,\"scheduledCnt\":0,\"seatCharm\":0,\"seatCnt\":0,\"sumCharm\":48952,\"sumIncome\":166}},{\"detail\":[{\"charm\":2,\"income\":2,\"time\":\"02/24\"},{\"charm\":0,\"income\":0,\"time\":\"02/25\"},{\"charm\":1,\"income\":1,\"time\":\"02/26\"},{\"charm\":0,\"income\":0,\"time\":\"02/27\"},{\"charm\":864,\"income\":3,\"time\":\"02/28\"},{\"charm\":0,\"income\":0,\"time\":\"03/01\"},{\"charm\":0,\"income\":0,\"time\":\"03/02\"}],\"player\":{\"band\":\"180006592\",\"id\":1376213950928013954,\"name\":\"A7\",\"photo\":\"https://cdnoffice.lizhi.fm/user/2023/11/07/3041049758065315842.jpg\"},\"sum\":{\"allMicGift\":\"\",\"allMicGiftAmount\":0,\"dayMicAmount\":0,\"hostCharmSum\":0,\"hostCnt\":0,\"lightGift\":\"100*3\",\"lightGiftAmount\":3,\"notDoneScore\":0,\"scheduledCnt\":0,\"seatCharm\":0,\"seatCnt\":0,\"sumCharm\":867,\"sumIncome\":6}},{\"detail\":[{\"charm\":2,\"income\":2,\"time\":\"02/24\"},{\"charm\":0,\"income\":0,\"time\":\"02/25\"},{\"charm\":1,\"income\":1,\"time\":\"02/26\"},{\"charm\":0,\"income\":0,\"time\":\"02/27\"},{\"charm\":576,\"income\":2,\"time\":\"02/28\"},{\"charm\":0,\"income\":0,\"time\":\"03/01\"},{\"charm\":0,\"income\":0,\"time\":\"03/02\"}],\"player\":{\"band\":\"180005976\",\"id\":1376214187151217794,\"name\":\"A8\",\"photo\":\"https://cdnoffice.lizhi.fm/user/2023/11/07/3041050032943223298.jpg\"},\"sum\":{\"allMicGift\":\"\",\"allMicGiftAmount\":0,\"dayMicAmount\":0,\"hostCharmSum\":0,\"hostCnt\":0,\"lightGift\":\"100*2\",\"lightGiftAmount\":2,\"notDoneScore\":0,\"scheduledCnt\":0,\"seatCharm\":0,\"seatCnt\":0,\"sumCharm\":579,\"sumIncome\":5}},{\"detail\":[{\"charm\":2,\"income\":2,\"time\":\"02/24\"},{\"charm\":0,\"income\":0,\"time\":\"02/25\"},{\"charm\":1,\"income\":1,\"time\":\"02/26\"},{\"charm\":0,\"income\":0,\"time\":\"02/27\"},{\"charm\":19296,\"income\":67,\"time\":\"02/28\"},{\"charm\":0,\"income\":0,\"time\":\"03/01\"},{\"charm\":0,\"income\":0,\"time\":\"03/02\"}],\"player\":{\"band\":\"180006769\",\"id\":1376216504286086018,\"name\":\"A9\",\"photo\":\"https://cdnoffice.lizhi.fm/user/2023/11/07/3041051519001909250.jpg\"},\"sum\":{\"allMicGift\":\"\",\"allMicGiftAmount\":0,\"dayMicAmount\":0,\"hostCharmSum\":0,\"hostCnt\":0,\"lightGift\":\"100*67\",\"lightGiftAmount\":67,\"notDoneScore\":0,\"scheduledCnt\":0,\"seatCharm\":0,\"seatCnt\":0,\"sumCharm\":19299,\"sumIncome\":70}},{\"detail\":[{\"charm\":2,\"income\":2,\"time\":\"02/24\"},{\"charm\":0,\"income\":0,\"time\":\"02/25\"},{\"charm\":1,\"income\":1,\"time\":\"02/26\"},{\"charm\":0,\"income\":0,\"time\":\"02/27\"},{\"charm\":19008,\"income\":66,\"time\":\"02/28\"},{\"charm\":0,\"income\":0,\"time\":\"03/01\"},{\"charm\":0,\"income\":0,\"time\":\"03/02\"}],\"player\":{\"band\":\"180006345\",\"id\":1376216727624387714,\"name\":\"A10\",\"photo\":\"https://cdnoffice.lizhi.fm/user/2023/11/07/3041051742340209154.jpg\"},\"sum\":{\"allMicGift\":\"\",\"allMicGiftAmount\":0,\"dayMicAmount\":0,\"hostCharmSum\":0,\"hostCnt\":0,\"lightGift\":\"100*66\",\"lightGiftAmount\":66,\"notDoneScore\":0,\"scheduledCnt\":0,\"seatCharm\":0,\"seatCnt\":0,\"sumCharm\":19011,\"sumIncome\":69}},{\"detail\":[{\"charm\":2,\"income\":2,\"time\":\"02/24\"},{\"charm\":0,\"income\":0,\"time\":\"02/25\"},{\"charm\":3,\"income\":3,\"time\":\"02/26\"},{\"charm\":0,\"income\":0,\"time\":\"02/27\"},{\"charm\":68225,\"income\":225,\"time\":\"02/28\"},{\"charm\":0,\"income\":0,\"time\":\"03/01\"},{\"charm\":0,\"income\":0,\"time\":\"03/02\"}],\"player\":{\"band\":\"99998888\",\"id\":1386652551113644802,\"name\":\"不睡觉啦\",\"photo\":\"https://cdnoffice.lizhi.fm/user/2023/11/07/3041077441277039106.jpg\"},\"sum\":{\"allMicGift\":\"100*2\",\"allMicGiftAmount\":84,\"dayMicAmount\":0,\"hostCharmSum\":0,\"hostCnt\":0,\"lightGift\":\"100*224\",\"lightGiftAmount\":224,\"notDoneScore\":0,\"scheduledCnt\":0,\"seatCharm\":0,\"seatCnt\":0,\"sumCharm\":68230,\"sumIncome\":230}},{\"detail\":[{\"charm\":2,\"income\":2,\"time\":\"02/24\"},{\"charm\":0,\"income\":0,\"time\":\"02/25\"},{\"charm\":1,\"income\":1,\"time\":\"02/26\"},{\"charm\":0,\"income\":0,\"time\":\"02/27\"},{\"charm\":19008,\"income\":66,\"time\":\"02/28\"},{\"charm\":0,\"income\":0,\"time\":\"03/01\"},{\"charm\":0,\"income\":0,\"time\":\"03/02\"}],\"player\":{\"band\":\"180006464\",\"id\":1391990761670705410,\"name\":\"A11\",\"photo\":\"https://cdnoffice.lizhi.fm/user/2023/11/07/3041051942056188930.jpg\"},\"sum\":{\"allMicGift\":\"\",\"allMicGiftAmount\":0,\"dayMicAmount\":0,\"hostCharmSum\":0,\"hostCnt\":0,\"lightGift\":\"100*66\",\"lightGiftAmount\":66,\"notDoneScore\":0,\"scheduledCnt\":0,\"seatCharm\":0,\"seatCnt\":0,\"sumCharm\":19011,\"sumIncome\":69}},{\"detail\":[{\"charm\":2,\"income\":2,\"time\":\"02/24\"},{\"charm\":0,\"income\":0,\"time\":\"02/25\"},{\"charm\":1,\"income\":1,\"time\":\"02/26\"},{\"charm\":0,\"income\":0,\"time\":\"02/27\"},{\"charm\":5473,\"income\":20,\"time\":\"02/28\"},{\"charm\":0,\"income\":0,\"time\":\"03/01\"},{\"charm\":0,\"income\":0,\"time\":\"03/02\"}],\"player\":{\"band\":\"180006374\",\"id\":1396994907524556930,\"name\":\"A6\",\"photo\":\"https://cdnoffice.lizhi.fm/user/2023/11/07/3041049472449988098.jpg\"},\"sum\":{\"allMicGift\":\"\",\"allMicGiftAmount\":0,\"dayMicAmount\":0,\"hostCharmSum\":0,\"hostCnt\":0,\"lightGift\":\"100*19\",\"lightGiftAmount\":19,\"notDoneScore\":0,\"scheduledCnt\":0,\"seatCharm\":0,\"seatCnt\":0,\"sumCharm\":5476,\"sumIncome\":23}},{\"detail\":[{\"charm\":0,\"income\":0,\"time\":\"02/24\"},{\"charm\":0,\"income\":0,\"time\":\"02/25\"},{\"charm\":0,\"income\":0,\"time\":\"02/26\"},{\"charm\":0,\"income\":0,\"time\":\"02/27\"},{\"charm\":0,\"income\":0,\"time\":\"02/28\"},{\"charm\":0,\"income\":0,\"time\":\"03/01\"},{\"charm\":0,\"income\":0,\"time\":\"03/02\"}],\"player\":{\"band\":\"180006278\",\"id\":1386652918333353474,\"name\":\"还好哈哈哈\",\"photo\":\"https://cdnoffice.lizhi.fm/user/2019/10/10/2764783519893950466.jpg\"},\"sum\":{\"allMicGift\":\"\",\"allMicGiftAmount\":0,\"dayMicAmount\":0,\"hostCharmSum\":0,\"hostCnt\":1,\"lightGift\":\"\",\"lightGiftAmount\":0,\"notDoneScore\":0,\"scheduledCnt\":0,\"seatCharm\":0,\"seatCnt\":0,\"sumCharm\":0,\"sumIncome\":0}}]";
        CheckInController checkInController = new CheckInController();
        List<WaveCheckInRoomStatisticBean> list = JsonUtil.loadsArray(dataJson, WaveCheckInRoomStatisticBean.class);

//        List<DynamicColTable.Row<Object>> charmRow = checkInController.buildRow(list, CheckInMetricsEnum.CHARM);
//        List<DynamicColTable.Row<Object>> incomeRow = checkInController.buildRow(list, CheckInMetricsEnum.INCOME);
//
        WaveCheckInRoomStatisticBean sum = checkInController.sumWaveCheckInRoomStatisticList(list);
        System.out.println("sum = " + sum);
    }

    /**
     * 导出麦序福利厅明细
     * <ul>
     *     <li>厅主视角: 不限家族, 当前厅, 的数据</li>
     *     <li>家族长视角: 当前家族, 选中厅, 的数据</li>
     * </ul>
     *
     * @param param 参数
     * @return 结果
     */
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.ROOM, RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN})
    @GetMapping("/room/export")
    public ResultVO<Void> exportCheckInRoomDetail(@Validated ExportCheckInRoomDetailParam param) {
        ContextUtils.getContext().addReqLog("`exportScore={}`dateType={}`startDate={}`endDate={}",
                param.getExportScore(), param.getDateType(), param.getStartDate(), param.getEndDate());
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        // 导出当前厅, 如果不是厅主角色必须传njId
        boolean exportCurrent = Objects.equals(param.getExportScore(), ExportCheckInRoomDetailParam.ExportScoreEnum.CURRENT);
        if (exportCurrent && !ContextUtils.getContext().isRoom() && param.getNjId() == null) {
            return ResultVO.failure("请选择厅");
        }
        // 注意如果加高级管理角色, 需要传familyId
        Long familyId = dataScopeHandler.getFamilyForFamily();
        ContextUtils.getContext().addReqLog("`familyId={}", familyId);
        // njId -> UserBean
        TreeMap<Long, UserBean> njIdUserBeanMap;
        if (ContextUtils.getContext().isRoom() || exportCurrent) {
            long njId = ContextUtils.getContext().isRoom() ? ContextUtils.getContext().getSubjectId() : param.getNjId();
            njIdUserBeanMap = getSingleUserBeanMapByUserId(appId, njId);
        } else if (ContextUtils.getContext().isFamilyAdmin()) {
            // 高级管理，只导出权限内的厅
            List<Long> njIds = ContextUtils.getContext().getRoomResource();
            List<UserBean> njBeans = userHandler.getUserByIds(njIds);
            njIdUserBeanMap = njBeans.stream().collect(Collectors.toMap(UserBean::getId, v->v, (v1, v2) -> v1, TreeMap::new));
        } else {
            Validate.notNull(familyId, "家族ID不能为空");
            njIdUserBeanMap = getNjUserBeanMapByFamilyId(familyId);
        }
        ContextUtils.getContext().addReqLog("`njIds={}", njIdUserBeanMap.keySet());
        if (MapUtils.isEmpty(njIdUserBeanMap)) {
            return ResultVO.failure("获取厅信息失败");
        }
        // 文件名
        String fileName;
        if ((ContextUtils.getContext().isFamily() || ContextUtils.getContext().isFamilyAdmin()) && !exportCurrent) {
            // 导出全部厅, 文件名包含家族名
            FamilyBean familyBean = userFamilyHandler.getFamilyById(ContextUtils.getContext().getSubjectId());
            if (familyBean == null) {
                return ResultVO.failure("获取家族信息失败");
            }
            fileName = sanitizeFamilyFileName(familyBean);
        } else {
            // 导出当前厅, 文件名包含厅名
            UserBean userBean = njIdUserBeanMap.entrySet().iterator().next().getValue();
            fileName = sanitizeRoomFileName(userBean);
        }
        // sheetId -> param
        TreeMap<Long, ExportCheckInRoomSheetParam> sheetIdParamMap = getExportCheckInRoomSheetParamMap(param, appId, njIdUserBeanMap);

        // sheet
        DynamicColTable dynamicColTable = new DynamicColTable();
        dynamicColTable.clearSheet();
        for (Map.Entry<Long, ExportCheckInRoomSheetParam> entry : sheetIdParamMap.entrySet()) {
            dynamicColTable.addSheet(entry.getValue().getSheetName(), entry.getKey());
        }
        dynamicColTable.putCol("昵称/时间");
        dynamicColTable.putCol("ID");
        // 明细数据缓存, 每个njId会构建两个sheet, 为避免重复请求, 使用缓存
        ConcurrentHashMap<RequestGetCheckInRoomStatistic, List<WaveCheckInRoomStatisticBean>> detailResultCache
                = new ConcurrentHashMap<>();
        return fileExportHandler.exportDynamicFile(fileName, dynamicColTable, (sheet, pageNo, pageSize) -> {
            // sheetId -> njId -> roomId
            ExportCheckInRoomSheetParam sheetParam = sheetIdParamMap.get(sheet.getId());
            Result<Long> roomIdByNjId = getRoomIdByNjId(appId, sheetParam.getNjId());
            if (RpcResult.isFail(roomIdByNjId)) {
                return PageVO.empty();
            }
            Long roomId = roomIdByNjId.target();
            // 先读缓存, 不在缓存中再请求
            RequestGetCheckInRoomStatistic req = checkInConverter.toRequestGetCheckInRoomStatistic(sheetParam, roomId, familyId);
            List<WaveCheckInRoomStatisticBean> list;
            if (detailResultCache.get(req) != null) {
                list = detailResultCache.get(req);
            } else {
                Result<ResponseGetCheckInRoomStatistic> result = waveCheckInDataService.getCheckInRoomStatistic(req);
                if (RpcResult.isFail(result)) {
                    log.warn("getCheckInRoomStatistic fail, req: {}, rCode: {}, message: {}", req, result.rCode(), result.getMessage());
                    list = new ArrayList<>();
                } else {
                    //合计行
                    list = result.target().getList();
                    WaveCheckInRoomStatisticBean rowSum = sumWaveCheckInRoomStatisticList(list);
                    WaveCheckInUserBean sumPlayer = new WaveCheckInUserBean();
                    sumPlayer.setId(-1L);
                    sumPlayer.setName("合计");
                    sumPlayer.setBand("");
                    rowSum.setPlayer(sumPlayer);
                    list.add(0, rowSum);
                }
                detailResultCache.put(req, list);
            }
            if (CollectionUtils.isEmpty(list)) {
                return PageVO.empty();
            }
            // 构建行数据
            List<DynamicColTable.Row<Object>> rows = buildExportCheckInRoomDetailRows(list, sheetParam);
            return PageVO.of(rows.size(), rows);
        });
    }

    /**
     * 构建厅打卡明细行数据
     */
    private List<DynamicColTable.Row<Object>> buildExportCheckInRoomDetailRows(List<WaveCheckInRoomStatisticBean> list, ExportCheckInRoomSheetParam sheetParam) {
        List<DynamicColTable.Row<Object>> rows = new ArrayList<>();
        for (WaveCheckInRoomStatisticBean statisticBean : list) {
            DynamicColTable.Row<Object> row = new DynamicColTable.Row<>();
            // 昵称
            row.putFreezeCol(Objects.toString(statisticBean.getPlayer().getName(), StringUtils.EMPTY));
            // 波段号
            row.putFreezeCol(Objects.toString(statisticBean.getPlayer().getBand(), StringUtils.EMPTY));
            List<WaveCheckInUserRecordSumBean> detail = statisticBean.getDetail();
            WaveCheckInUserSumBean sum = statisticBean.getSum();
            if (Objects.equals(sheetParam.getMetrics(), CheckInMetricsEnum.CHARM)) {
                // 魅力值收入明细
                appendCharmDetailColumns(row, detail);
                // 其他统计字段
                appendCharmStatisticSum(row, sum);
            } else if (Objects.equals(sheetParam.getMetrics(), CheckInMetricsEnum.INCOME)) {
                // 钻石收入明细
                appendIncomeDetailColumns(row, detail);
                // 其他统计字段
                appendIncomeStatisticSum(row, sum);
            }
            rows.add(row);
        }
        return rows;
    }

    /**
     * 获取厅打卡明细 sheet 导出参数
     *
     * @param param 导出参数
     * @param appId appId
     * @param njIdUserBeanMap njId -> UserBean
     * @return sheetId -> ExportCheckInRoomSheetParam
     */
    private TreeMap<Long, ExportCheckInRoomSheetParam> getExportCheckInRoomSheetParamMap(ExportCheckInRoomDetailParam param, int appId, TreeMap<Long, UserBean> njIdUserBeanMap) {
        TreeMap<Long, ExportCheckInRoomSheetParam> sheetIdParamMap = new TreeMap<>();
        AtomicLong sheetIdGenerator = new AtomicLong();
        if (param.getDateType().equals(CheckInDateTypeEnum.HOUR) || param.getDateType().equals(CheckInDateTypeEnum.WEEK)){
            // 如果是小时和周维度，sheet 仅展示厅昵称即可
            for (Map.Entry<Long, UserBean> entry : njIdUserBeanMap.entrySet()) {
                Long njId = entry.getKey();
                UserBean njUserBean = njIdUserBeanMap.get(njId);
                String sheetName = njUserBean.getName();
                ExportCheckInRoomSheetParam exportParam = checkInConverter.toExportCheckInRoomSheetParam(
                        param, appId, njId, sheetName);
                sheetIdParamMap.put(sheetIdGenerator.getAndIncrement(), exportParam);
            }
        } else if (param.getDateType().equals(CheckInDateTypeEnum.DAY)) {
            // 如果是天，展示厅昵称打卡总计、厅昵称24小时魅力值明细
            for (Map.Entry<Long, UserBean> entry : njIdUserBeanMap.entrySet()) {
                Long njId = entry.getKey();
                UserBean njUserBean = njIdUserBeanMap.get(njId);
                String sumSheetName = sanitizeRoomSumSheetName(njUserBean);
                ExportCheckInRoomSheetParam exportParam = checkInConverter.toExportCheckInRoomSheetParam(
                        param, appId, njId, sumSheetName);
                String charmSheetName = sanitizeRoomCharmSheetName(njUserBean);
                ExportCheckInRoomSheetParam charmExportParam = checkInConverter.toExportCheckInRoomSheetParam(
                        param, appId, njId, charmSheetName);
                sheetIdParamMap.put(sheetIdGenerator.getAndIncrement(), charmExportParam);
                sheetIdParamMap.put(sheetIdGenerator.getAndIncrement(), exportParam);
            }
        } else {
            log.warn("不支持的导出类型: {}", param.getDateType());
        }

        return sheetIdParamMap;
    }

    private List<DynamicColTable.Row<Object>> buildRow(List<WaveCheckInRoomStatisticBean> templist, CheckInMetricsEnum metrics){
        ArrayList<WaveCheckInRoomStatisticBean> list = new ArrayList<>(templist);
        //合计行
        WaveCheckInRoomStatisticBean rowSum = sumWaveCheckInRoomStatisticList(list);
        WaveCheckInUserBean sumPlayer = new WaveCheckInUserBean();
        sumPlayer.setId(-1L);
        sumPlayer.setName("合计");
        sumPlayer.setBand("");
        rowSum.setPlayer(sumPlayer);
        list.add(rowSum);
        List<DynamicColTable.Row<Object>> rows = new ArrayList<>();
        for (WaveCheckInRoomStatisticBean statisticBean : list) {
            DynamicColTable.Row<Object> row = new DynamicColTable.Row<>();
            // 昵称
            row.putFreezeCol(Objects.toString(statisticBean.getPlayer().getName(), StringUtils.EMPTY));
            // 波段号
            row.putFreezeCol(Objects.toString(statisticBean.getPlayer().getBand(), StringUtils.EMPTY));
            List<WaveCheckInUserRecordSumBean> detail = statisticBean.getDetail();
            WaveCheckInUserSumBean sum = statisticBean.getSum();
            if (Objects.equals(metrics, CheckInMetricsEnum.CHARM)) {
                // 魅力值收入明细
                appendCharmDetailColumns(row, detail);
                // 其他统计字段
                appendCharmStatisticSum(row, sum);
            } else if (Objects.equals(metrics, CheckInMetricsEnum.INCOME)) {
                // 钻石收入明细
                appendIncomeDetailColumns(row, detail);
                // 其他统计字段
                appendIncomeStatisticSum(row, sum);
            }
            rows.add(row);
        }
        return rows;
    }

    private TreeMap<Long, UserBean> getNjUserBeanMapByFamilyId(long familyId) {
        // njId -> UserBean
        TreeMap<Long, UserBean> njIdUserBeanMap = new TreeMap<>();
        List<RoomSignBean> roomSignBeans = userFamilyHandler.getGuildAllRooms(familyId);
        for (RoomSignBean roomSignBean : roomSignBeans) {
            njIdUserBeanMap.put(roomSignBean.getId(), roomSignBean);
        }
        return njIdUserBeanMap;
    }

    private TreeMap<Long, UserBean> getSingleUserBeanMapByUserId(int appId, long userId) {
        // userId -> UserBean
        TreeMap<Long, UserBean> userIdUserBeanMap = new TreeMap<>();
        UserBean userBean = userHandler.getUserById(appId, userId);
        if (userBean != null) {
            userIdUserBeanMap.put(userBean.getId(), userBean);
        }
        return userIdUserBeanMap;
    }

    private UserBean getUserBeanByUserId(int appId, long userId) {
        return userHandler.getUserById(appId, userId);
    }

    private String sanitizeFileName(String originName, String defaultValue) {
        // 替换非法的文件名字符
        // \（反斜杠）、/（正斜杠）、:（冒号）、*（星号）、?（问号）、"（双引号）、<（小于）、>（大于）、|（竖线） 是windows系统禁止的字符
        //  （空格）、_（下划线）、-（连字符）、.（点号）是不能用于文件名开头或结尾的字符
        // @（at）、#（井号）、$（美元符号）、&（和号）、;（分号）是纯人工额外添加的禁止字符避免其他特殊情况
        String sanitizedName = originName.replaceAll("[\\\\/:*?\"<>|\\s._@#$&;-]", StringUtils.EMPTY);
        if (StringUtils.isBlank(sanitizedName)) {
            return defaultValue;
        }
        return sanitizedName;
    }

    private String sanitizeFamilyFileName(FamilyBean familyBean) {
        return sanitizeFileName(familyBean.getFamilyName(), "导出全部") + "_工会打卡明细表";
    }

    private String sanitizeRoomFileName(UserBean userBean) {
        return sanitizeFileName(userBean.getName(), userBean.getBand()) + "_厅打卡明细表";
    }

    private String sanitizePlayerFileName(UserBean userBean) {
        return sanitizeFileName(userBean.getName(), userBean.getBand()) + "_主播打卡明细表";
    }

    private String sanitizeSheetName(String sheetName, String defaultValue) {
        // 替换非法的工作表名字符
        // /（正斜杠）、\（反斜杠）、?（问号）、*（星号）、:（冒号）、[（左中括号）、]（右中括号）、'（单引号）
        // 详见 https://support.microsoft.com/en-us/office/rename-a-worksheet-3f1f7148-ee83-404d-8ef0-9ff99fbad1f9
        String sanitizedName = sheetName.replaceAll("[/\\\\?*:\\[\\]']", StringUtils.EMPTY);
        if (StringUtils.isBlank(sanitizedName)) {
            return defaultValue;
        }
        return sanitizedName;
    }

    private String joinSheetName(String sheetPrefix, String sheetSuffix) {
        // 详见 https://support.microsoft.com/en-us/office/rename-a-worksheet-3f1f7148-ee83-404d-8ef0-9ff99fbad1f9
        int maxSheetNameLength = 31;
        int availablePrefixLength = Math.max(maxSheetNameLength - sheetSuffix.length(), 0);
        String retainPrefix = availablePrefixLength >= sheetPrefix.length() ? sheetPrefix : sheetPrefix.substring(0, availablePrefixLength);
        return retainPrefix + sheetSuffix;
    }

    private String sanitizeRoomSumSheetName(UserBean njUserBean) {
        if (njUserBean == null) {
            return "xx_厅打卡总计";
        }
        String sanitizedName = sanitizeSheetName(njUserBean.getName(), njUserBean.getBand());
        return joinSheetName(sanitizedName, "_厅打卡总计");
    }


    private String sanitizeRoomCharmSheetName(UserBean njUserBean) {
        if (njUserBean == null) {
            return "xx_厅24小时魅力值明细";
        }
        String sanitizedName = sanitizeSheetName(njUserBean.getName(), njUserBean.getBand());
        return joinSheetName(sanitizedName, "_厅24小时魅力值明细");
    }

    private String sanitizeRoomIncomeSheetName(UserBean njUserBean) {
        if (njUserBean == null) {
            return "xx_厅钻石明细";
        }
        String sanitizedName = sanitizeSheetName(njUserBean.getName(), njUserBean.getBand());
        return joinSheetName(sanitizedName, "_厅钻石明细");
    }

    private String sanitizePlayerCharmSheetName(UserBean njUserBean) {
        if (njUserBean == null) {
            return "xx_主播魅力值明细";
        }
        String sanitizedName = sanitizeSheetName(njUserBean.getName(), njUserBean.getBand());
        return joinSheetName(sanitizedName, "_主播魅力值明细");
    }

    private String sanitizePlayerIncomeSheetName(UserBean njUserBean) {
        if (njUserBean == null) {
            return "xx_主播钻石明细";
        }
        String sanitizedName = sanitizeSheetName(njUserBean.getName(), njUserBean.getBand());
        return joinSheetName(sanitizedName, "_主播钻石明细");
    }

    private void appendCharmDetailColumns(DynamicColTable.Row<Object> row, List<WaveCheckInUserRecordSumBean> detailBeans) {
        if (CollectionUtils.isEmpty(detailBeans)) {
            return;
        }
        // 魅力值收入明细
        for (WaveCheckInUserRecordSumBean detailBean : detailBeans) {
            row.putCol(detailBean.getTime(), Objects.toString(detailBean.getCharm(), StringUtils.EMPTY));
        }
    }

    private void appendIncomeDetailColumns(DynamicColTable.Row<Object> row, List<WaveCheckInUserRecordSumBean> sumBeans) {
        if (CollectionUtils.isEmpty(sumBeans)) {
            return;
        }
        // 钻石收入明细
        for (WaveCheckInUserRecordSumBean sumBean : sumBeans) {
            row.putCol(sumBean.getTime(), Objects.toString(sumBean.getIncome(), StringUtils.EMPTY));
        }
    }

    private void appendCharmStatisticSum(DynamicColTable.Row<Object> row, WaveCheckInUserSumBean sum) {
        if (sum == null) {
            return;
        }
        // 合计魅力值
        row.putCol("合计魅力值", Objects.toString(sum.getSumCharm(), StringUtils.EMPTY));
        // 未完成任务
        row.putCol("未完成任务", Objects.toString(sum.getNotDoneScore(), StringUtils.EMPTY));
        // 有效麦序
        row.putCol("有效麦序", Objects.toString(sum.getSeatCnt(), StringUtils.EMPTY));
        // 有效麦序魅力值
        row.putCol("有效麦序魅力值", Objects.toString(sum.getSeatCharm(), StringUtils.EMPTY));
        // 排档数
        row.putCol("排档数", Objects.toString(sum.getScheduledCnt(), StringUtils.EMPTY));
        // 主持档数
        row.putCol("主持档数", Objects.toString(sum.getHostCnt(), StringUtils.EMPTY));
        // 主持档魅力值
        row.putCol("主持档魅力值", Objects.toString(sum.getHostCharmSum(), StringUtils.EMPTY));
        // 收光记录
        row.putCol("收光记录", Objects.toString(sum.getLightGift(), StringUtils.EMPTY));
        // 收光奖励（元）
        row.putCol("收光奖励（元）", Objects.toString(sum.getLightGiftAmount(), StringUtils.EMPTY));
        // 全麦记录
        row.putCol("全麦记录", Objects.toString(sum.getAllMicGift(), StringUtils.EMPTY));
        // 全麦奖励
        row.putCol("全麦奖励（元）", Objects.toString(sum.getAllMicGiftAmount(), StringUtils.EMPTY));
        // 日麦序奖励
        row.putCol("日麦序奖励（元）", Objects.toString(sum.getDayMicAmount(), StringUtils.EMPTY));
    }

    private void appendIncomeStatisticSum(DynamicColTable.Row<Object> row, WaveCheckInUserSumBean sum) {
        if (sum == null) {
            return;
        }
        // 合计钻石值
        row.putCol("合计钻石值", Objects.toString(sum.getSumIncome(), StringUtils.EMPTY));
    }

    /**
     * 获取麦序福利主播汇总数据
     * <ul>
     *     <li>主播视角: 不限家族, 不限厅, 当前主播, 的数据</li>
     *     <li>厅主视角: 不限家族, 当前厅, 选中主播, 的数据</li>
     *     <li>家族长视角: 当前家族, 不限厅, 选中主播, 的数据</li>
     * </ul>
     *
     * @param param 参数
     * @return 结果
     */
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.PLAYER, RoleEnum.ROOM, RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN})
    @GetMapping("/player/sum")
    public ResultVO<ResponseGetCheckInPlayerSum> getCheckInPlayerSum(GetCheckInPlayerSumParam param) {
        // 如果不是主播请求, 需要传playerId
        if (!ContextUtils.getContext().isPlayer() && param.getPlayerId() == null) {
            return ResultVO.failure("请选择主播");
        }
        Long playerId = ContextUtils.getContext().isPlayer() ? ContextUtils.getContext().getSubjectId() : param.getPlayerId();
        ContextUtils.getContext().addReqLog("`playerId={}`startDate={}`endDate={}", playerId, param.getStartDate(), param.getEndDate());
        // 注意如果加高级管理角色, 需要传familyId
        Long familyId = dataScopeHandler.getFamilyForFamily();
        ContextUtils.getContext().addReqLog("`familyId={}", familyId);
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Long roomId;
        if (ContextUtils.getContext().isRoom()) {
            Result<Long> roomIdByNjId = getRoomIdByNjId(appId, ContextUtils.getContext().getSubjectId());
            if (RpcResult.isFail(roomIdByNjId)) {
                return ResultVO.failure(roomIdByNjId.getMessage());
            }
            roomId = roomIdByNjId.target();
        } else {
            roomId = null;
        }
        ContextUtils.getContext().addReqLog("`roomId={}", roomId);
        RequestGetCheckInPlayerSum req = checkInConverter.toRequestGetCheckInPlayerSum(param, appId, playerId, familyId, roomId);
        Result<ResponseGetCheckInPlayerSum> result = waveCheckInDataService.getCheckInPlayerSum(req);
        if (RpcResult.isFail(result)) {
            log.info("getCheckInPlayerSum fail, req: {}, rCode: {}, message: {}", req, result.rCode(), result.getMessage());
            if (result.rCode() == CommonService.PARAM_ERROR) {
                return ResultVO.failure(result.getMessage());
            } else {
                return ResultVO.failure();
            }
        }
        ResponseGetCheckInPlayerSum resp = result.target();
        log.info("getCheckInPlayerSum success, resp: {}", resp);
        return ResultVO.success(resp);
    }

    /**
     * 获取麦序福利主播明细数据
     * <ul>
     *     <li>主播视角: 不限家族, 不限厅, 当前主播, 的数据</li>
     *     <li>厅主视角: 不限家族, 当前厅, 选中主播, 的数据</li>
     *     <li>家族长视角: 当前家族, 不限厅, 选中主播, 的数据</li>
     * </ul>
     *
     * @param param 参数
     * @return 结果
     */
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.PLAYER, RoleEnum.ROOM, RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN})
    @GetMapping("/player/detail")
    public ResultVO<GetCheckInPlayerDetailResult> getCheckInPlayerDetail(GetCheckInPlayerDetailParam param) {
        // 如果不是主播请求, 需要传playerId
        if (!ContextUtils.getContext().isPlayer() && param.getPlayerId() == null) {
            return ResultVO.failure("请选择主播");
        }
        Long playerId = ContextUtils.getContext().isPlayer() ? ContextUtils.getContext().getSubjectId() : param.getPlayerId();
        ContextUtils.getContext().addReqLog("`playerId={}`startDate={}`endDate={}",
                playerId, param.getStartDate(), param.getEndDate());
        // 注意如果加高级管理角色, 需要传familyId
        Long familyId = dataScopeHandler.getFamilyForFamily();
        ContextUtils.getContext().addReqLog("`familyId={}", familyId);
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Long roomId;
        if (ContextUtils.getContext().isRoom()) {
            Result<Long> roomIdByNjId = getRoomIdByNjId(appId, ContextUtils.getContext().getSubjectId());
            if (RpcResult.isFail(roomIdByNjId)) {
                return ResultVO.failure(roomIdByNjId.getMessage());
            }
            roomId = roomIdByNjId.target();
        } else {
            roomId = null;
        }
        ContextUtils.getContext().addReqLog("`roomId={}", roomId);
        RequestGetCheckInPlayerStatistic req = checkInConverter.toRequestGetCheckInPlayerStatistic(param, appId, playerId, familyId, roomId);
        Result<ResponseGetCheckInPlayerStatistic> result = waveCheckInDataService.getCheckInPlayerStatistic(req);
        if (RpcResult.isFail(result)) {
            log.info("getCheckInPlayerDetail fail, req: {}, rCode: {}, message: {}", req, result.rCode(), result.getMessage());
            if (result.rCode() == CommonService.PARAM_ERROR) {
                return ResultVO.failure(result.getMessage());
            } else {
                return ResultVO.failure();
            }
        }
        ResponseGetCheckInPlayerStatistic resp = result.target();
        log.debug("getCheckInPlayerDetail success, resp: {}", resp);
        return ResultVO.success(checkInConverter.toGetCheckInPlayerDetailResult(resp));
    }

    /**
     * 导出麦序福利主播明细
     * <ul>
     *     <li>主播视角: 不限家族, 不限厅, 当前主播, 的数据</li>
     *     <li>厅主视角: 不限家族, 当前厅, 选中主播, 的数据</li>
     *     <li>家族长视角: 当前家族, 不限厅, 选中主播, 的数据</li>
     * </ul>
     *
     * @param param 参数
     * @return 结果
     */
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.PLAYER, RoleEnum.ROOM, RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN})
    @GetMapping("/player/export")
    public ResultVO<Void> exportCheckInPlayerDetail(@Validated ExportCheckInPlayerDetailParam param) {
        // 如果不是主播请求, 需要传playerId
        if (!ContextUtils.getContext().isPlayer() && param.getPlayerId() == null) {
            return ResultVO.failure("请选择主播");
        }
        Long playerId = ContextUtils.getContext().isPlayer() ? ContextUtils.getContext().getSubjectId() : param.getPlayerId();
        ContextUtils.getContext().addReqLog("`playerId={}`startDate={}`endDate={}",
                playerId, param.getStartDate(), param.getEndDate());
        // 注意如果加高级管理角色, 需要传familyId
        Long familyId = dataScopeHandler.getFamilyForFamily();
        ContextUtils.getContext().addReqLog("`familyId={}", familyId);
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Long roomId;
        if (ContextUtils.getContext().isRoom()) {
            Result<Long> roomIdByNjId = getRoomIdByNjId(appId, ContextUtils.getContext().getSubjectId());
            if (RpcResult.isFail(roomIdByNjId)) {
                return ResultVO.failure(roomIdByNjId.getMessage());
            }
            roomId = roomIdByNjId.target();
        } else {
            roomId = null;
        }
        ContextUtils.getContext().addReqLog("`roomId={}", roomId);
        UserBean playerUserBean = getUserBeanByUserId(appId, playerId);
        if (playerUserBean == null) {
            return ResultVO.failure("获取主播信息失败");
        }
        String fileName = sanitizePlayerFileName(playerUserBean);
        DynamicColTable dynamicColTable = new DynamicColTable();
        dynamicColTable.clearSheet();
        long charmSheetId = 0L;
        String charmSheetName = sanitizePlayerCharmSheetName(playerUserBean);
        dynamicColTable.addSheet(charmSheetName, charmSheetId);
        long incomeSheetId = 1L;
        String incomeSheetName = sanitizePlayerIncomeSheetName(playerUserBean);
        dynamicColTable.addSheet(incomeSheetName, incomeSheetId);
        dynamicColTable.putCol("日期/时间");
        return fileExportHandler.exportDynamicFile(fileName, dynamicColTable, (sheet, pageNo, pageSize) -> {
            RequestGetCheckInPlayerStatistic req = checkInConverter.toRequestGetCheckInPlayerStatistic(param, appId, familyId, roomId);
            Result<ResponseGetCheckInPlayerStatistic> result = waveCheckInDataService.getCheckInPlayerStatistic(req);
            if (RpcResult.isFail(result)) {
                log.warn("getCheckInPlayerStatistic fail, req: {}, rCode: {}, message: {}", req, result.rCode(), result.getMessage());
                return PageVO.empty();
            }
            List<WaveCheckInPlayerStatisticBean> list = result.target().getList();
            // 构建行数据
            List<DynamicColTable.Row<Object>> rows = new ArrayList<>();
            for (WaveCheckInPlayerStatisticBean statisticBean : list) {
                DynamicColTable.Row<Object> row = new DynamicColTable.Row<>();
                // 日期/时间
                row.putFreezeCol(Objects.toString(statisticBean.getDate(), StringUtils.EMPTY));
                List<WaveCheckInUserRecordSumBean> detail = statisticBean.getDetail();
                WaveCheckInUserSumBean sum = statisticBean.getSum();
                if (Objects.equals(sheet.getId(), charmSheetId)) {
                    // 魅力值收入明细
                    appendCharmDetailColumns(row, detail);
                    // 其他统计字段
                    appendCharmStatisticSum(row, sum);
                } else if (Objects.equals(sheet.getId(), incomeSheetId)) {
                    // 钻石收入明细
                    appendIncomeDetailColumns(row, detail);
                    // 其他统计字段
                    appendIncomeStatisticSum(row, sum);
                }
                rows.add(row);
            }
            return PageVO.of(rows.size(), rows);
        });
    }
}
