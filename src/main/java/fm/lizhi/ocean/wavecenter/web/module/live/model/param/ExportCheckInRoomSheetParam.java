package fm.lizhi.ocean.wavecenter.web.module.live.model.param;

import fm.lizhi.ocean.wavecenter.api.live.constants.CheckInDateTypeEnum;
import fm.lizhi.ocean.wavecenter.web.module.live.constant.CheckInMetricsEnum;
import lombok.Data;

import java.util.List;

/**
 * 导出麦序福利厅sheet的参数
 */
@Data
public class ExportCheckInRoomSheetParam {

    /**
     * sheet名称
     */
    private String sheetName;

    /**
     * 应用id
     */
    private Integer appId;

    /**
     * 厅主id
     */
    private Long njId;

    /**
     * 统计的时间类型
     */
    private CheckInDateTypeEnum dateType;

    /**
     * 开始时间毫秒时间戳, 包含
     */
    private Long startDate;

    /**
     * 结束时间毫秒时间戳, 包含
     */
    private Long endDate;

    /**
     *  指标列表
     */
    private List<String> metrics;
}
